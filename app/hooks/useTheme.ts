import { useTheme as useThemeContext } from '../context/ThemeContext';

// Re-export the theme hook for easier imports
export const useTheme = useThemeContext;

// Helper function to get themed class names
export const getThemedClassName = (lightClass: string, darkClass: string, isDark: boolean): string => {
  return isDark ? darkClass : lightClass;
};

// Helper function to get themed styles
export const getThemedStyle = (lightStyle: object, darkStyle: object, isDark: boolean): object => {
  return isDark ? darkStyle : lightStyle;
};
