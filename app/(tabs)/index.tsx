import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { useTheme } from '../context/ThemeContext';

const index = () => {
	const { colors, colorScheme } = useTheme();

	return (
		<View
			className='flex-1 items-center justify-center p-4'
			style={{ backgroundColor: colors.background }}>
			<Text
				className='text-2xl font-bold mb-4'
				style={{ color: colors.text }}>
				Welcome to UploadDoc
			</Text>

			<Text
				className='text-lg mb-2'
				style={{ color: colors.text }}>
				Current theme: {colorScheme}
			</Text>

			<Text
				className='text-base mb-6 text-center'
				style={{ color: colors.secondary }}>
				NativeWind is working! The theme automatically switches based on your
				device settings.
			</Text>

			<TouchableOpacity
				className='px-6 py-3 rounded-lg mb-4'
				style={{ backgroundColor: colors.primary }}>
				<Text className='text-white font-semibold text-center'>
					Primary Button
				</Text>
			</TouchableOpacity>

			<TouchableOpacity
				className='px-6 py-3 rounded-lg mb-4'
				style={{ backgroundColor: colors.accent }}>
				<Text className='text-white font-semibold text-center'>
					Accent Button
				</Text>
			</TouchableOpacity>

			<View
				className='p-4 rounded-lg mt-4'
				style={{ backgroundColor: colors.secondary }}>
				<Text className='text-text text-center'>Secondary Background</Text>
			</View>
		</View>
	);
};

export default index;
